// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IStaderSDPriceFeed} from "tests/interfaces/internal/IStaderSDPriceFeed.sol";
import {IValueInterpreter} from "tests/interfaces/internal/IValueInterpreter.sol";

address constant STADER_ORACLE_ADDRESS = 0xF64bAe65f6f2a5277571143A24FaaFDFC0C2a737;
address constant SD_TOKEN_ADDRESS = 0x30D20208d987713f46DFD34EF128Bb16C404D10f;

/**
 * @title StaderSDPriceFeed Vulnerability Demonstration
 * @notice This test demonstrates that StaderSDPriceFeed only returns base market price,
 *         excluding accumulated rewards that SD token holders earn through delegation,
 *         protocol fees, and buyback mechanisms.
 */
contract StaderSDPriceFeedVulnerabilityTest is IntegrationTest {
    IStaderSDPriceFeed internal priceFeed;

    function setUp() public override {
        setUpMainnetEnvironment(ETHEREUM_BLOCK_TIME_SENSITIVE);
        priceFeed = __deployPriceFeed();
    }

    function __deployPriceFeed() private returns (IStaderSDPriceFeed) {
        address addr = deployCode(
            "StaderSDPriceFeed.sol", abi.encode(SD_TOKEN_ADDRESS, STADER_ORACLE_ADDRESS, address(wrappedNativeToken))
        );
        return IStaderSDPriceFeed(addr);
    }

    /**
     * @notice Demonstrates that StaderSDPriceFeed excludes accumulated rewards
     *  This test shows the current implementation only returns base market price,
     *      not accounting for SD token rewards from delegation, protocol fees, and buybacks
     */
    function test_StaderSDPriceFeed_ExcludesAccumulatedRewards() public {
        // Test current implementation
        uint256 sdAmount = 1e18; // 1 SD token
        (address[] memory underlyings, uint256[] memory amounts) =
            priceFeed.calcUnderlyingValues(SD_TOKEN_ADDRESS, sdAmount);

        // Demonstrate impact with hypothetical 15% accumulated rewards
        uint256 hypotheticalRewards = 150e15; // 0.15 ETH (15% rewards)
        uint256 totalRealValue = amounts[0] + hypotheticalRewards;
        uint256 undervaluationPercent = (hypotheticalRewards * 100) / totalRealValue;

        // Basic assertions
        assertEq(underlyings.length, 1, "Should return one underlying asset");
        assertEq(underlyings[0], address(wrappedNativeToken), "Underlying should be WETH");
        assertGt(amounts[0], 0, "Should return positive value");
        assertTrue(priceFeed.isSupportedAsset(SD_TOKEN_ADDRESS), "Should support SD token");

        // Demonstrate significant undervaluation
        assertGt(undervaluationPercent, 10, "Undervaluation should be significant");
    }

    /**
     * @notice Shows that the price feed works functionally but excludes rewards
     * @dev This demonstrates the architectural issue - function works as coded,
     *      but design is flawed because it doesn't account for accumulated rewards
     */
    function test_StaderSDPriceFeed_FunctionalButIncomplete() public {
        // Add derivative to value interpreter
        addDerivative({
            _valueInterpreter: IValueInterpreter(getValueInterpreterAddressForVersion(EnzymeVersion.Current)),
            _tokenAddress: SD_TOKEN_ADDRESS,
            _skipIfRegistered: false,
            _priceFeedAddress: address(priceFeed)
        });

        // Test returns reasonable USD value but excludes accumulated rewards
        assertValueInUSDForVersion({
            _version: EnzymeVersion.Current,
            _asset: SD_TOKEN_ADDRESS,
            _amount: assetUnit(IERC20(SD_TOKEN_ADDRESS)),
            _expected: 935646379201838789 // Base price only, excludes rewards
        });
    }
}

/**
 * @title Documentation of SD Token Rewards (Not Included in Price Feed)
 * @notice This contract serves as documentation of the rewards that StaderSDPriceFeed
 *         should include but currently excludes
 */
contract SDTokenRewardsDocumentation {
    /**
     * @notice SD Utility Pool Delegation Rewards
     * @dev Source: https://everstake.one/blog/what-is-staders-sd-utility-pool-we-tried-it
     *
     * Mechanism:
     * - SD holders delegate tokens to utility pool
     * - Earn up to 14% APY
     * - Rewards from utilization fees + 10% of node operator commissions
     *
     * Status: Unclaimed until withdrawal - NOT reflected in getSDPriceInETH()
     */
    function sdUtilityPoolRewards() external pure returns (string memory) {
        return "Up to 14% APY from delegation - NOT included in StaderSDPriceFeed";
    }

    /**
     * @notice Protocol Fee Distribution
     * @dev Source: https://www.staderlabs.com/blog/diving-deeper-into-sd-tokenomics/
     *
     * Mechanism:
     * - 5% of user staking rewards charged as protocol fee
     * - Significant share redirected to SD stakers
     *
     * Status: Pending distribution - NOT reflected in getSDPriceInETH()
     */
    function protocolFeeDistribution() external pure returns (string memory) {
        return "Protocol fee share to SD stakers - NOT included in StaderSDPriceFeed";
    }

    /**
     * @notice Buyback Value Accrual
     * @dev Source: https://www.staderlabs.com/blog/diving-deeper-into-sd-tokenomics/
     *
     * Mechanism:
     * - 20% of Stader annual revenue for quarterly buybacks
     * - Creates deflationary pressure and value accrual
     *
     * Status: Accumulated value - NOT reflected in getSDPriceInETH()
     */
    function buybackValueAccrual() external pure returns (string memory) {
        return "Buyback value accrual - NOT included in StaderSDPriceFeed";
    }
}
