// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IStaderSDPriceFeed} from "tests/interfaces/internal/IStaderSDPriceFeed.sol";
import {IValueInterpreter} from "tests/interfaces/internal/IValueInterpreter.sol";
import {IComptrollerLib} from "tests/interfaces/internal/IComptrollerLib.sol";
import {IVaultLib} from "tests/interfaces/internal/IVaultLib.sol";

/**
 * @title StaderSDPriceFeed Vulnerability Proof of Concept
 * @notice Demonstrates critical vulnerability where StaderSDPriceFeed excludes accumulated rewards,
 *         leading to systematic GAV undervaluation and direct theft of user funds during redemptions
 */
contract StaderSDPriceFeedVulnerabilityPoC is IntegrationTest {
    // Constants
    address constant SD_TOKEN_ADDRESS = 0x30D20208d987713f46DFD34EF128Bb16C404D10f;
    address constant STADER_ORACLE_ADDRESS = 0xF64bAe65f6f2a5277571143A24FaaFDFC0C2a737;

    // Test contracts
    IStaderSDPriceFeed internal vulnerablePriceFeed;
    MockStaderOracleWithRewards internal mockOracle;
    CorrectStaderSDPriceFeed internal correctPriceFeed;

    // Test accounts and contracts
    address internal fundOwner;
    address internal vaultProxy;
    address internal comptrollerProxy;
    address internal investor1;
    address internal investor2;

    IERC20 internal sdToken;
    IERC20 internal denominationAsset;

    // Test data
    uint256 constant INITIAL_SD_BALANCE = 1000e18; // 1000 SD tokens
    uint256 constant BASE_SD_PRICE = 1e18; // 1 ETH per SD
    uint256 constant ACCUMULATED_REWARDS = 150e18; // 15% rewards (150 ETH)

    function setUp() public override {
        setUpMainnetEnvironment(ETHEREUM_BLOCK_TIME_SENSITIVE);

        // Deploy mock oracle that simulates rewards accumulation
        mockOracle = new MockStaderOracleWithRewards();

        // Deploy vulnerable price feed (current implementation)
        address vulnerablePriceFeedAddr = deployCode(
            "StaderSDPriceFeed.sol", abi.encode(SD_TOKEN_ADDRESS, address(mockOracle), address(wrappedNativeToken))
        );
        vulnerablePriceFeed = IStaderSDPriceFeed(vulnerablePriceFeedAddr);

        // Deploy correct price feed for comparison
        correctPriceFeed =
            new CorrectStaderSDPriceFeed(SD_TOKEN_ADDRESS, address(mockOracle), address(wrappedNativeToken));

        // Setup tokens and accounts
        sdToken = IERC20(SD_TOKEN_ADDRESS);
        denominationAsset = IERC20(address(wrappedNativeToken));
        investor1 = makeAddr("Investor1");
        investor2 = makeAddr("Investor2");

        // Create fund with vulnerable price feed
        (comptrollerProxy, vaultProxy, fundOwner) =
            createTradingFundForVersion({_version: EnzymeVersion.Current, _denominationAsset: denominationAsset});

        // Register SD token with vulnerable price feed
        addDerivative({
            _valueInterpreter: IValueInterpreter(getValueInterpreterAddressForVersion(EnzymeVersion.Current)),
            _tokenAddress: SD_TOKEN_ADDRESS,
            _skipIfRegistered: false,
            _priceFeedAddress: address(vulnerablePriceFeed)
        });

        // Setup initial state
        deal(SD_TOKEN_ADDRESS, vaultProxy, INITIAL_SD_BALANCE);
        deal(address(denominationAsset), investor1, 1000e18);
        deal(address(denominationAsset), investor2, 1000e18);

        // Set initial SD price
        mockOracle.setBasePrice(BASE_SD_PRICE);
    }

    /**
     * @notice Main PoC demonstrating the vulnerability
     * @dev This test proves that StaderSDPriceFeed excludes accumulated rewards,
     *      leading to GAV undervaluation and direct theft during redemptions
     */
    function test_StaderSDPriceFeed_ExcludesRewards_CausesDirectTheft() public {
        console.log("=== STADER SD PRICE FEED VULNERABILITY PROOF OF CONCEPT ===\n");

        // STEP 1: Initial fund state
        console.log("STEP 1: Initial Fund State");
        console.log("----------------------------------------");

        uint256 initialSDBalance = sdToken.balanceOf(vaultProxy);
        uint256 initialGAV = IComptrollerLib(comptrollerProxy).calcGav();
        uint256 initialShares = IVaultLib(vaultProxy).totalSupply();

        console.log("SD Token Balance: %s SD", initialSDBalance / 1e18);
        console.log("Initial GAV: %s ETH", initialGAV / 1e18);
        console.log("Total Shares: %s shares", initialShares / 1e18);
        console.log("Share Price: %s ETH per share", (initialGAV * 1e18 / initialShares) / 1e18);
        console.log("");

        // STEP 2: Simulate SD rewards accumulation
        console.log("STEP 2: SD Rewards Accumulation");
        console.log("----------------------------------------");

        // Simulate 15% rewards accumulation through:
        // - SD Utility Pool delegation (up to 14% APY)
        // - Protocol fee distribution to SD stakers
        // - Buyback value accrual (20% of annual revenue)
        mockOracle.addAccumulatedRewards(ACCUMULATED_REWARDS);

        console.log("Accumulated Rewards: %s ETH", ACCUMULATED_REWARDS / 1e18);
        console.log("Base SD Price: %s ETH per SD", mockOracle.getSDPriceInETH() / 1e18);
        console.log("Real SD Value (with rewards): %s ETH per SD", mockOracle.getSDPriceWithRewards() / 1e18);
        console.log("");

        // STEP 3: Demonstrate GAV undervaluation
        console.log("STEP 3: GAV Undervaluation Analysis");
        console.log("----------------------------------------");

        uint256 reportedGAV = IComptrollerLib(comptrollerProxy).calcGav();
        uint256 realGAV = (mockOracle.getSDPriceWithRewards() * initialSDBalance) / 1e18;
        uint256 undervaluation = realGAV - reportedGAV;
        uint256 undervaluationPercent = (undervaluation * 100) / realGAV;

        console.log("Reported GAV (vulnerable): %s ETH", reportedGAV / 1e18);
        console.log("Real GAV (with rewards): %s ETH", realGAV / 1e18);
        console.log("Undervaluation: %s ETH", undervaluation / 1e18);
        console.log("Undervaluation Percentage: %s%%", undervaluationPercent);
        console.log("");

        // STEP 4: Demonstrate impact on share pricing
        console.log("STEP 4: Share Pricing Impact");
        console.log("----------------------------------------");

        uint256 vulnerableSharePrice = IComptrollerLib(comptrollerProxy).calcGrossShareValue();
        uint256 correctSharePrice = (realGAV * 1e18) / initialShares;

        console.log("Vulnerable Share Price: %s ETH per share", vulnerableSharePrice / 1e18);
        console.log("Correct Share Price: %s ETH per share", correctSharePrice / 1e18);
        console.log("Price Difference: %s ETH per share", (correctSharePrice - vulnerableSharePrice) / 1e18);
        console.log("");

        // STEP 5: Demonstrate direct theft during redemption
        console.log("STEP 5: Direct Theft During Redemption");
        console.log("----------------------------------------");

        uint256 sharesToRedeem = 100e18; // Redeem 100 shares

        // Calculate redemption value using vulnerable price feed
        uint256 vulnerableRedemptionValue = (vulnerableSharePrice * sharesToRedeem) / 1e18;

        // Calculate what redemption value should be with correct pricing
        uint256 correctRedemptionValue = (correctSharePrice * sharesToRedeem) / 1e18;

        // Calculate direct loss
        uint256 directLoss = correctRedemptionValue - vulnerableRedemptionValue;
        uint256 lossPercent = (directLoss * 100) / correctRedemptionValue;

        console.log("Shares to Redeem: %s shares", sharesToRedeem / 1e18);
        console.log("Vulnerable Redemption Value: %s ETH", vulnerableRedemptionValue / 1e18);
        console.log("Correct Redemption Value: %s ETH", correctRedemptionValue / 1e18);
        console.log("DIRECT LOSS: %s ETH", directLoss / 1e18);
        console.log("Loss Percentage: %s%%", lossPercent);
        console.log("");

        // STEP 6: Demonstrate unfair advantage for new investors
        console.log("STEP 6: Unfair Advantage for New Investors");
        console.log("----------------------------------------");

        uint256 investmentAmount = 115e18; // New investor deposits 115 ETH

        // Calculate shares received with vulnerable pricing
        uint256 vulnerableSharesReceived = (investmentAmount * 1e18) / vulnerableSharePrice;

        // Calculate shares that should be received with correct pricing
        uint256 correctSharesReceived = (investmentAmount * 1e18) / correctSharePrice;

        // Calculate unfair advantage
        uint256 extraShares = vulnerableSharesReceived - correctSharesReceived;
        uint256 advantagePercent = (extraShares * 100) / correctSharesReceived;

        console.log("Investment Amount: %s ETH", investmentAmount / 1e18);
        console.log("Vulnerable Shares Received: %s shares", vulnerableSharesReceived / 1e18);
        console.log("Correct Shares Received: %s shares", correctSharesReceived / 1e18);
        console.log("UNFAIR ADVANTAGE: %s extra shares", extraShares / 1e18);
        console.log("Advantage Percentage: %s%%", advantagePercent);
        console.log("");

        // ASSERTIONS TO PROVE VULNERABILITY
        console.log("=== VULNERABILITY PROOF ASSERTIONS ===");

        // Prove GAV undervaluation
        assertGt(realGAV, reportedGAV, "Real GAV must be higher than reported GAV");
        assertGt(undervaluationPercent, 10, "Undervaluation must be significant (>10%)");

        // Prove direct theft during redemptions
        assertGt(directLoss, 0, "Direct loss must occur during redemptions");
        assertGt(lossPercent, 10, "Loss percentage must be significant (>10%)");

        // Prove unfair advantage for new investors
        assertGt(extraShares, 0, "New investors must receive unfair advantage");
        assertGt(advantagePercent, 10, "Advantage must be significant (>10%)");

        console.log("All assertions passed - Vulnerability confirmed");
        console.log("CRITICAL severity: Direct theft of user funds");
        console.log("Impact: %s%% undervaluation leading to systematic wealth transfer", undervaluationPercent);
    }

    /**
     * @notice Demonstrates comparison between vulnerable and correct implementations
     */
    function test_CompareVulnerableVsCorrectImplementation() public {
        console.log("=== VULNERABLE VS CORRECT IMPLEMENTATION COMPARISON ===\n");

        // Add rewards to simulate real-world scenario
        mockOracle.addAccumulatedRewards(ACCUMULATED_REWARDS);

        // Test vulnerable implementation (current StaderSDPriceFeed)
        (address[] memory vulnerableUnderlyings, uint256[] memory vulnerableAmounts) =
            vulnerablePriceFeed.calcUnderlyingValues(SD_TOKEN_ADDRESS, 1e18);

        // Test correct implementation
        (address[] memory correctUnderlyings, uint256[] memory correctAmounts) =
            correctPriceFeed.calcUnderlyingValues(SD_TOKEN_ADDRESS, 1e18);

        console.log("Vulnerable Implementation:");
        console.log("- Returns: %s ETH per SD", vulnerableAmounts[0] / 1e18);
        console.log("- Excludes: %s ETH in rewards", ACCUMULATED_REWARDS / 1e18);
        console.log("");

        console.log("Correct Implementation:");
        console.log("- Returns: %s ETH per SD", correctAmounts[0] / 1e18);
        console.log("- Includes: %s ETH in rewards", ACCUMULATED_REWARDS / 1e18);
        console.log("");

        uint256 difference = correctAmounts[0] - vulnerableAmounts[0];
        console.log(
            "Difference: %s ETH per SD (%s%% undervaluation)", difference / 1e18, (difference * 100) / correctAmounts[0]
        );

        // Prove the difference equals the accumulated rewards
        assertEq(difference, ACCUMULATED_REWARDS, "Difference should equal accumulated rewards");
    }
}

/**
 * @title Mock Stader Oracle with Rewards Simulation
 * @notice Simulates Stader Oracle behavior with accumulated rewards tracking
 */
contract MockStaderOracleWithRewards {
    uint256 private basePrice;
    uint256 private accumulatedRewards;

    function setBasePrice(uint256 _price) external {
        basePrice = _price;
    }

    function addAccumulatedRewards(uint256 _rewards) external {
        accumulatedRewards += _rewards;
    }

    /**
     * @notice Current implementation - only returns base price (VULNERABLE)
     * @dev This is what the real Stader Oracle returns - excludes rewards
     */
    function getSDPriceInETH() external view returns (uint256) {
        return basePrice; // Only base price, NO rewards
    }

    /**
     * @notice What the price should be - includes accumulated rewards
     * @dev This represents the real value of SD tokens including all rewards
     */
    function getSDPriceWithRewards() external view returns (uint256) {
        return basePrice + accumulatedRewards; // Base price + accumulated rewards
    }

    function getAccumulatedRewards() external view returns (uint256) {
        return accumulatedRewards;
    }
}

/**
 * @title Correct Stader SD Price Feed Implementation
 * @notice Shows how StaderSDPriceFeed should work - including accumulated rewards
 */
contract CorrectStaderSDPriceFeed {
    address private immutable SD_TOKEN;
    MockStaderOracleWithRewards private immutable ORACLE;
    address private immutable WETH_ADDRESS;
    uint256 private constant SD_TOKEN_UNIT = 10 ** 18;

    constructor(address _sdToken, address _oracle, address _weth) {
        SD_TOKEN = _sdToken;
        ORACLE = MockStaderOracleWithRewards(_oracle);
        WETH_ADDRESS = _weth;
    }

    /**
     * @notice Correct implementation - includes accumulated rewards
     * @dev This is how calcUnderlyingValues should work
     */
    function calcUnderlyingValues(address, uint256 _derivativeAmount)
        external
        view
        returns (address[] memory underlyings_, uint256[] memory underlyingAmounts_)
    {
        underlyings_ = new address[](1);
        underlyings_[0] = WETH_ADDRESS;

        underlyingAmounts_ = new uint256[](1);
        // ✅ CORRECT: Include accumulated rewards in valuation
        underlyingAmounts_[0] = ORACLE.getSDPriceWithRewards() * _derivativeAmount / SD_TOKEN_UNIT;

        return (underlyings_, underlyingAmounts_);
    }

    function isSupportedAsset(address _asset) external view returns (bool) {
        return _asset == SD_TOKEN;
    }
}
