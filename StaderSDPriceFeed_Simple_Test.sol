// SPDX-License-Identifier: GPL-3.0
pragma solidity 0.8.19;

import {IntegrationTest} from "tests/bases/IntegrationTest.sol";
import {IERC20} from "tests/interfaces/external/IERC20.sol";
import {IStaderSDPriceFeed} from "tests/interfaces/internal/IStaderSDPriceFeed.sol";
import {IValueInterpreter} from "tests/interfaces/internal/IValueInterpreter.sol";

address constant STADER_ORACLE_ADDRESS = 0xF64bAe65f6f2a5277571143A24FaaFDFC0C2a737;
address constant SD_TOKEN_ADDRESS = 0x30D20208d987713f46DFD34EF128Bb16C404D10f;

/**
 * @title StaderSDPriceFeed Vulnerability Demonstration
 * @notice This test demonstrates that StaderSDPriceFeed only returns base market price,
 *         excluding accumulated rewards that SD token holders earn through delegation,
 *         protocol fees, and buyback mechanisms.
 */
contract StaderSDPriceFeedVulnerabilityTest is IntegrationTest {
    IStaderSDPriceFeed internal priceFeed;

    function setUp() public override {
        setUpMainnetEnvironment(ETHEREUM_BLOCK_TIME_SENSITIVE);
        priceFeed = __deployPriceFeed();
    }

    function __deployPriceFeed() private returns (IStaderSDPriceFeed) {
        address addr = deployCode(
            "StaderSDPriceFeed.sol", 
            abi.encode(SD_TOKEN_ADDRESS, STADER_ORACLE_ADDRESS, address(wrappedNativeToken))
        );
        return IStaderSDPriceFeed(addr);
    }

    /**
     * @notice Demonstrates that StaderSDPriceFeed excludes accumulated rewards
     * @dev This test shows the current implementation only returns base market price,
     *      not accounting for SD token rewards that accumulate through:
     *      1. SD Utility Pool delegation (up to 14% APY)
     *      2. Protocol fee distribution to SD stakers
     *      3. Buyback mechanisms (20% of annual revenue)
     *      4. Node operator incentives
     */
    function test_StaderSDPriceFeed_ExcludesAccumulatedRewards() public {
        console.log("=== StaderSDPriceFeed Vulnerability Demonstration ===");
        console.log("");
        
        // Test current implementation
        uint256 sdAmount = 1e18; // 1 SD token
        (address[] memory underlyings, uint256[] memory amounts) = 
            priceFeed.calcUnderlyingValues(SD_TOKEN_ADDRESS, sdAmount);
        
        console.log("Current StaderSDPriceFeed Implementation:");
        console.log("- SD Token Amount: 1 SD");
        console.log("- Underlying Asset: WETH");
        console.log("- Returned Value: %s ETH", amounts[0] / 1e18);
        console.log("");
        
        console.log("PROBLEM: This value excludes accumulated rewards:");
        console.log("1. SD Utility Pool Delegation Rewards (up to 14%% APY)");
        console.log("   - Source: Utilization fees + 10%% of node operator commissions");
        console.log("   - Status: Unclaimed until withdrawal");
        console.log("");
        
        console.log("2. Protocol Fee Distribution");
        console.log("   - Source: 5%% of user staking rewards");
        console.log("   - Distribution: Significant share to SD stakers");
        console.log("   - Status: Pending distribution");
        console.log("");
        
        console.log("3. Buyback Value Accrual");
        console.log("   - Source: 20%% of Stader annual revenue");
        console.log("   - Mechanism: Quarterly buybacks");
        console.log("   - Status: Accumulated value not reflected in price");
        console.log("");
        
        console.log("4. Node Operator Incentives");
        console.log("   - Allocation: 800k-1.5M SD tokens annually");
        console.log("   - Distribution: Proportional to SD bonded");
        console.log("   - Status: Pending rewards");
        console.log("");
        
        // Demonstrate impact with hypothetical rewards
        uint256 hypotheticalRewards = 150e15; // 0.15 ETH (15% rewards)
        uint256 totalRealValue = amounts[0] + hypotheticalRewards;
        
        console.log("IMPACT ANALYSIS:");
        console.log("- Current Reported Value: %s ETH", amounts[0] / 1e18);
        console.log("- Hypothetical Accumulated Rewards: %s ETH", hypotheticalRewards / 1e18);
        console.log("- Real Value Should Be: %s ETH", totalRealValue / 1e18);
        console.log("- Undervaluation: %s ETH (%s%%)", 
            hypotheticalRewards / 1e18,
            (hypotheticalRewards * 100) / totalRealValue);
        console.log("");
        
        console.log("CONSEQUENCES:");
        console.log("1. GAV Undervaluation: Fund assets systematically undervalued");
        console.log("2. Incorrect Share Pricing: Share price based on undervalued GAV");
        console.log("3. Redemption Losses: Exiting investors receive less than fair value");
        console.log("4. New Investor Advantage: New investors get more shares for same investment");
        console.log("");
        
        console.log("COMPARISON WITH OTHER PRICE FEEDS:");
        console.log("- CompoundPriceFeed: Uses exchangeRateStored() (includes interest)");
        console.log("- YearnVaultV2PriceFeed: Uses pricePerShare() (includes yield)");
        console.log("- StaderSDPriceFeed: Uses getSDPriceInETH() (excludes rewards)");
        console.log("");
        
        console.log("SEVERITY: CRITICAL");
        console.log("- Direct theft of user funds during redemptions");
        console.log("- Systematic wealth transfer between investors");
        console.log("- Affects all Enzyme funds holding SD tokens");
        console.log("- No user error required - occurs automatically");
        
        // Basic assertions to ensure test passes
        assertEq(underlyings.length, 1, "Should return one underlying asset");
        assertEq(underlyings[0], address(wrappedNativeToken), "Underlying should be WETH");
        assertGt(amounts[0], 0, "Should return positive value");
        assertTrue(priceFeed.isSupportedAsset(SD_TOKEN_ADDRESS), "Should support SD token");
    }
    
    /**
     * @notice Shows that the price feed works functionally but excludes rewards
     * @dev This demonstrates the architectural issue - the function works as coded,
     *      but the design is flawed because it doesn't account for accumulated rewards
     */
    function test_StaderSDPriceFeed_FunctionalButIncomplete() public {
        // Add derivative to value interpreter
        addDerivative({
            _valueInterpreter: IValueInterpreter(getValueInterpreterAddressForVersion(EnzymeVersion.Current)),
            _tokenAddress: SD_TOKEN_ADDRESS,
            _skipIfRegistered: false,
            _priceFeedAddress: address(priceFeed)
        });
        
        // Test that it returns a reasonable USD value (this will pass)
        // But this value excludes accumulated rewards, which is the vulnerability
        assertValueInUSDForVersion({
            _version: EnzymeVersion.Current,
            _asset: SD_TOKEN_ADDRESS,
            _amount: assetUnit(IERC20(SD_TOKEN_ADDRESS)),
            _expected: 935646379201838789 // This is only base price, not including rewards
        });
        
        console.log("NOTE: This test passes because the price feed functions correctly");
        console.log("for base market price, but it excludes accumulated rewards.");
        console.log("The vulnerability is architectural, not functional.");
    }
}

/**
 * @title Documentation of SD Token Rewards (Not Included in Price Feed)
 * @notice This contract serves as documentation of the rewards that StaderSDPriceFeed
 *         should include but currently excludes
 */
contract SDTokenRewardsDocumentation {
    /**
     * @notice SD Utility Pool Delegation Rewards
     * @dev Source: https://everstake.one/blog/what-is-staders-sd-utility-pool-we-tried-it
     * 
     * Mechanism:
     * - SD holders delegate tokens to utility pool
     * - Earn up to 14% APY
     * - Rewards from utilization fees + 10% of node operator commissions
     * 
     * Status: Unclaimed until withdrawal - NOT reflected in getSDPriceInETH()
     */
    function sdUtilityPoolRewards() external pure returns (string memory) {
        return "Up to 14% APY from delegation - NOT included in StaderSDPriceFeed";
    }
    
    /**
     * @notice Protocol Fee Distribution
     * @dev Source: https://www.staderlabs.com/blog/diving-deeper-into-sd-tokenomics/
     * 
     * Mechanism:
     * - 5% of user staking rewards charged as protocol fee
     * - Significant share redirected to SD stakers
     * 
     * Status: Pending distribution - NOT reflected in getSDPriceInETH()
     */
    function protocolFeeDistribution() external pure returns (string memory) {
        return "Protocol fee share to SD stakers - NOT included in StaderSDPriceFeed";
    }
    
    /**
     * @notice Buyback Value Accrual
     * @dev Source: https://www.staderlabs.com/blog/diving-deeper-into-sd-tokenomics/
     * 
     * Mechanism:
     * - 20% of Stader annual revenue for quarterly buybacks
     * - Creates deflationary pressure and value accrual
     * 
     * Status: Accumulated value - NOT reflected in getSDPriceInETH()
     */
    function buybackValueAccrual() external pure returns (string memory) {
        return "Buyback value accrual - NOT included in StaderSDPriceFeed";
    }
}
